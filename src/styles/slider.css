/* Custom Range Slider Styles */
.range-slider {
  position: relative;
  height: 8px;
}

.range-slider input[type="range"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: transparent;
  -webkit-appearance: none;
  appearance: none;
  pointer-events: none;
  outline: none;
}

.range-slider input[type="range"]::-webkit-slider-track {
  height: 8px;
  background: transparent;
  border: none;
  outline: none;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #02164F;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: all;
  position: relative;
}

.range-slider input[type="range"]::-moz-range-track {
  height: 8px;
  background: transparent;
  border: none;
  outline: none;
}

.range-slider input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #02164F;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: all;
}

.range-slider input[type="range"]:focus {
  outline: none;
}

.range-slider input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(2, 22, 79, 0.2), 0 2px 8px rgba(0, 0, 0, 0.2);
}

.range-slider input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(2, 22, 79, 0.2), 0 2px 8px rgba(0, 0, 0, 0.2);
}

.range-slider input[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.1);
}

.range-slider input[type="range"]:active::-moz-range-thumb {
  transform: scale(1.1);
}
